import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import TypewriterText from '../../contact/components/TypewriterText';

const Services: React.FC = () => {
  const { t } = useTranslation();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  const services = [
    {
      key: 'propertyManagement',
      icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4'
    },
    {
      key: 'consulting',
      icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
    },
    {
      key: 'rental',
      icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6'
    }
  ];

  return (
    <div className="dark:bg-gradient-to-br dark:from-gray-800/50 dark:to-gray-900/50 min-h-screen py-16 backdrop-blur-sm">
      <div className="container-custom">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold text-theme-primary mb-4">
            <TypewriterText text={t('services.title')} delay={70} />
          </h1>
          <p className="text-xl text-theme-secondary">
            <TypewriterText text={t('services.subtitle')} delay={50} />
          </p>
        </motion.div>

        {/* Services Grid */}
        <motion.div
          className="grid md:grid-cols-3 gap-8 mb-16"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {services.map((service) => (
            <motion.div
              key={service.key}
              className="bg-theme-light dark:bg-gradient-to-br dark:from-gray-800/40 dark:to-gray-900/40 rounded-lg shadow-lg overflow-hidden border border-theme backdrop-blur-sm"
              variants={itemVariants}
              whileHover={{ y: -5, scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              {/* Icon */}
              <div className="p-6 bg-gradient-primary dark:bg-opacity-80 text-white flex justify-center backdrop-blur-sm">
                <motion.svg 
                  className="w-12 h-12" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                  initial={{ rotate: 0 }}
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.6 }}
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={service.icon} />
                </motion.svg>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-theme-primary mb-4">
                  <TypewriterText text={t(`services.${service.key}.title`)} delay={50} />
                </h3>
                <p className="text-theme-secondary mb-6">
                  {t(`services.${service.key}.description`)}
                </p>

                {/* Features */}
                <ul className="space-y-4">
                  {(t(`services.${service.key}.features`, { returnObjects: true }) as string[]).map((feature: string, index: number) => (
                    <motion.li
                      key={index}
                      className="flex items-center space-x-3 text-gray-600 dark:text-gray-300/90"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <motion.svg 
                        className="w-5 h-5 text-italian-green flex-shrink-0" 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                        whileHover={{ scale: 1.2 }}
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </motion.svg>
                      <span>{feature}</span>
                    </motion.li>
                  ))}
                </ul>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA Section */}
        <motion.div
          className="text-center mt-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
        >
          <Link to="/contact">
            <motion.button
              className="btn btn-primary px-8 py-3 text-lg"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {t('common.contact')}
            </motion.button>
          </Link>
        </motion.div>
      </div>
    </div>
  );
};

export default Services;
