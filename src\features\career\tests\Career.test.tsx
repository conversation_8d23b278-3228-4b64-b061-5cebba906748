import { describe, it, expect } from 'vitest';
import { render } from '@testing-library/react';
import { screen } from '@testing-library/dom';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import Career from '../components/Career';
import store from '../../../app/store';

const renderWithProviders = (component: React.ReactNode) => {
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('Career Component', () => {
  it('renders career page title', () => {
    renderWithProviders(<Career />);
    expect(screen.getByText(/join our team/i)).toBeInTheDocument();
  });

  it('displays job listings', () => {
    renderWithProviders(<Career />);
    expect(screen.getByText(/property manager/i)).toBeInTheDocument();
    expect(screen.getByText(/real estate agent/i)).toBeInTheDocument();
    expect(screen.getByText(/marketing specialist/i)).toBeInTheDocument();
  });

  it('shows benefits section', () => {
    renderWithProviders(<Career />);
    expect(screen.getByText(/why join us/i)).toBeInTheDocument();
    expect(screen.getByText(/growth opportunities/i)).toBeInTheDocument();
    expect(screen.getByText(/competitive package/i)).toBeInTheDocument();
    expect(screen.getByText(/great culture/i)).toBeInTheDocument();
  });

  it('renders apply buttons for each job listing', () => {
    renderWithProviders(<Career />);
    const applyButtons = screen.getAllByText(/apply now/i);
    expect(applyButtons.length).toBeGreaterThan(0);
  });
});
