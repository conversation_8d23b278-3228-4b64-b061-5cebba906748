import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import i18n from './config';

export interface LanguageState {
  currentLanguage: 'en' | 'fr';
  translations: Record<string, Record<string, string>>;
}

const initialState: LanguageState = {
  currentLanguage: 'en',
  translations: {
    en: {},
    fr: {}
  },
};

const languageSlice = createSlice({
  name: 'language',
  initialState,
  reducers: {
    changeLanguage: (state, action: PayloadAction<'en' | 'fr'>) => {
      state.currentLanguage = action.payload;
      i18n.changeLanguage(action.payload);
    },
    setTranslations: (state, action: PayloadAction<Record<string, Record<string, string>>>) => {
      state.translations = action.payload;
    }
  }
});

export const { changeLanguage, setTranslations } = languageSlice.actions;
export default languageSlice.reducer;
