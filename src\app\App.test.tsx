import React from 'react';
import { render } from '@testing-library/react';
import { screen } from '@testing-library/dom';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import store from './store';
import App from '../App';
import { act } from '@testing-library/react';

// Create a custom render function that includes providers
const renderWithProviders = (component: React.ReactNode) => {
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('App Component', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  test('renders main app layout', () => {
    // Remove the BrowserRouter since it's already included in App
    renderWithProviders(<App />);

    // Check if the header is rendered
    const headerElement = screen.getByRole('banner');
    expect(headerElement).toBeInTheDocument();

    // Check if the main navigation links are present
    expect(screen.getByText(/home/<USER>
    expect(screen.getByText(/properties/i)).toBeInTheDocument();
    expect(screen.getByText(/services/i)).toBeInTheDocument();
    expect(screen.getByText(/about/i)).toBeInTheDocument();

    // Check if theme and language toggles are present
    expect(screen.getByText('EN')).toBeInTheDocument();
    expect(screen.getByText('FR')).toBeInTheDocument();
    expect(screen.getByLabelText(/toggle theme/i)).toBeInTheDocument();
  });

  test('switches language correctly', async () => {
    renderWithProviders(<App />);

    // Find and click the French language button
    const frButton = screen.getByText('FR');
    await act(async () => {
      frButton.click();
    });

    // Check if navigation links are now in French
    expect(screen.getByText(/accueil/i)).toBeInTheDocument();
    expect(screen.getByText(/propriétés/i)).toBeInTheDocument();
    expect(screen.getByText(/services/i)).toBeInTheDocument();
    expect(screen.getByText(/à propos/i)).toBeInTheDocument();
  });
});
