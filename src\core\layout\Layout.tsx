import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../app/types';
import Header from '../navigation/Header';
import Footer from '../navigation/Footer';
import { CookieConsent } from '../components/CookieConsent';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { mode } = useSelector((state: RootState) => state.theme);

  React.useEffect(() => {
    if (mode === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [mode]);

  return (
    <div className={`min-h-screen flex flex-col bg-theme-light transition-colors duration-200 dark:bg-gradient-to-br dark:from-[#1F2937] dark:to-[#111827] dark:text-theme-light`}>
      <Header />
      <main className="flex-grow p-4">
        {children}
      </main>
      <Footer />
      <CookieConsent />
    </div>
  );
};

export default Layout;
