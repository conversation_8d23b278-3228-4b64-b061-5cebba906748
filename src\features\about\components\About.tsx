import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import TypewriterText from '../../contact/components/TypewriterText';

const About: React.FC = () => {
  const { t } = useTranslation();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  const values = [
    { title: 'Excellence', description: 'We strive for excellence in everything we do.' },
    { title: 'Integrity', description: 'We conduct our business with unwavering honesty.' },
    { title: 'Innovation', description: 'We embrace new technologies and ideas.' },
    { title: 'Client Focus', description: 'Our clients\' success is our success.' }
  ];

  const stats = [
    { value: '15+', label: 'Years Experience' },
    { value: '1000+', label: 'Happy Clients' },
    { value: '500+', label: 'Properties Managed' },
    { value: '98%', label: 'Client Satisfaction' }
  ];

  return (
    <div className="bg-theme-dark min-h-screen section-padding">
      <div className="container-custom">
        {/* Header */}
        <motion.div
          className="text-center mb-16 section-padding"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold text-theme-primary mb-4">
            <TypewriterText text={t('about.title')} delay={70} />
          </h1>
          <p className="text-xl text-theme-secondary">
            <TypewriterText text={t('about.subtitle')} delay={50} />
          </p>
        </motion.div>

        {/* Mission & Vision */}
        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <motion.div
            className="bg-theme-light rounded-lg shadow-lg p-8"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            whileHover={{ y: -5 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <motion.h2 
              className="text-2xl font-bold text-theme-primary mb-4"
              variants={itemVariants}
            >
              <TypewriterText text={t('about.mission.title')} delay={50} />
            </motion.h2>
            <motion.p 
              className="text-theme-secondary"
              variants={itemVariants}
            >
              <TypewriterText text={t('about.mission.description')} delay={30} />
            </motion.p>
          </motion.div>

          <motion.div
            className="bg-gradient-primary text-white rounded-lg shadow-lg p-8"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            whileHover={{ y: -5 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <motion.h2 
              className="text-2xl font-bold mb-4"
              variants={itemVariants}
            >
              <TypewriterText text={t('about.vision.title')} delay={50} />
            </motion.h2>
            <motion.p variants={itemVariants}>
              <TypewriterText text={t('about.vision.description')} delay={30} />
            </motion.p>
          </motion.div>
        </div>

        {/* Values */}
        <motion.section
          className="mb-16"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.h2 
            className="text-3xl font-bold text-theme-primary text-center mb-12"
            variants={itemVariants}
          >
            <TypewriterText text={t('about.values.title')} delay={50} />
          </motion.h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 section-padding">
            {values.map((value, index) => (
              <motion.div
                key={index}
                className="bg-theme-light rounded-lg shadow-lg p-6"
                variants={itemVariants}
                whileHover={{ y: -5, scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <h3 className="text-xl font-bold text-italian-green mb-4">
                  <TypewriterText text={value.title} delay={30} />
                </h3>
                <p className="text-theme-secondary">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Team */}
        <motion.section
          className="mb-16"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.h2 
            className="text-3xl font-bold text-theme-primary text-center mb-12"
            variants={itemVariants}
          >
            <TypewriterText text={t('about.team.title')} delay={50} />
          </motion.h2>
          <motion.div
            className="max-w-3xl mx-auto bg-theme-light rounded-lg shadow-lg overflow-hidden"
            variants={itemVariants}
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <div className="md:flex">
              <div className="md:flex-shrink-0">
                <div className="h-48 w-full md:w-48 bg-gray-300 dark:bg-gray-700"></div>
              </div>
              <div className="p-8">
                <div className="text-sm text-italian-green font-semibold tracking-wide">
                  {t('about.team.founder.position')}
                </div>
                <h3 className="mt-2 text-xl font-semibold text-theme-primary">
                  {t('about.team.founder.name')}
                </h3>
                <p className="mt-4 text-theme-secondary italic">
                  <TypewriterText text={t('about.team.founder.quote')} delay={50} />
                </p>
                <p className="mt-4 text-theme-secondary">
                  <TypewriterText text={t('about.team.founder.description')} delay={30} />
                </p>
              </div>
            </div>
          </motion.div>
        </motion.section>

        {/* Stats */}
        <motion.section
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="py-8 section-padding"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                className="text-center"
                variants={itemVariants}
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div
                  className="text-4xl font-bold text-theme-primary mb-2"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 100, delay: index * 0.1 }}
                >
                  {stat.value}
                </motion.div>
                <div className="text-theme-secondary">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.section>
      </div>
    </div>
  );
};

export default About;