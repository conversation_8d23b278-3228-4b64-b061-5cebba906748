import { describe, it, expect } from 'vitest';
import { render } from '@testing-library/react';
import { screen } from '@testing-library/dom';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import Properties from '../components/Properties';
import store from '../../../app/store';

const renderWithProviders = (component: React.ReactNode) => {
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('Properties Component', () => {
  it('renders properties list', () => {
    renderWithProviders(<Properties />);
    expect(screen.getByText(/properties/i)).toBeInTheDocument();
  });

  it('displays property cards', () => {
    renderWithProviders(<Properties />);
    const propertyCards = screen.getAllByRole('article');
    expect(propertyCards.length).toBeGreaterThan(0);
  });

  it('shows property details', () => {
    renderWithProviders(<Properties />);
    expect(screen.getByText(/price/i)).toBeInTheDocument();
    expect(screen.getByText(/location/i)).toBeInTheDocument();
  });
});
