import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { ChevronDownIcon, ChevronUpIcon, FunnelIcon } from '@heroicons/react/24/outline';
import type { RootState } from '../../../app/store';
import {
  setPriceRange,
  setLocation,
  setPropertyType,
  setListingType,
  resetFilters,
} from '../filtersSlice';

const PropertyFilters: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const filters = useSelector((state: RootState) => state.filters);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout>();

  const propertyTypes = [
    'apartment',
    'house',
    'villa',
    'office',
    'retail',
    'land'
  ];

  const handlePriceChange = (type: 'min' | 'max', value: string) => {
    const numValue = parseInt(value) || 0;
    dispatch(setPriceRange({
      ...filters.priceRange,
      [type]: numValue
    }));
  };

  const handleLocationChange = (value: string) => {
    clearTimeout(searchTimeout);
    setIsSearching(true);
    const timeout = setTimeout(() => {
      dispatch(setLocation(value));
      setIsSearching(false);
    }, 500);
    setSearchTimeout(timeout);
  };

  const handlePropertyTypeChange = (type: string) => {
    const newTypes = filters.propertyType.includes(type)
      ? filters.propertyType.filter(t => t !== type)
      : [...filters.propertyType, type];
    dispatch(setPropertyType(newTypes));
    setIsSearching(true);
    setTimeout(() => setIsSearching(false), 300);
  };

  const handleListingTypeChange = (type: 'all' | 'rent' | 'sale') => {
    dispatch(setListingType(type));
    setIsSearching(true);
    setTimeout(() => setIsSearching(false), 300);
  };

  const handleReset = () => {
    dispatch(resetFilters());
    setIsSearching(true);
    setTimeout(() => setIsSearching(false), 300);
  };

  return (
    <div className="relative mb-8">
      {/* Loading Indicator */}
      <AnimatePresence>
        {isSearching && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50 rounded-lg z-10 flex items-center justify-center"
          >
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
              <span className="text-white font-medium">{t('properties.filters.searching')}</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Filter Toggle Button */}
      <motion.button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between px-6 py-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
      >
        <div className="flex items-center space-x-2">
          <FunnelIcon className="h-5 w-5" />
          <span className="font-semibold">{t('properties.filters.title')}</span>
          {(filters.location || filters.propertyType.length > 0 || filters.listingType !== 'all' || 
            filters.priceRange.min !== 0 || filters.priceRange.max !== 3000000) && (
            <span className="ml-2 px-2 py-1 bg-italian-green text-white text-sm rounded-full">
              {t('properties.filters.active')}
            </span>
          )}
        </div>
        {isExpanded ? (
          <ChevronUpIcon className="h-5 w-5" />
        ) : (
          <ChevronDownIcon className="h-5 w-5" />
        )}
      </motion.button>

      {/* Filter Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="mt-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              {/* Price Range */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  {t('properties.filters.priceRange.title')}
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {t('properties.filters.priceRange.min')} ({t('properties.currency')})
                    </label>
                    <input
                      type="number"
                      value={filters.priceRange.min}
                      onChange={(e) => handlePriceChange('min', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-italian-green focus:border-italian-green dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {t('properties.filters.priceRange.max')} ({t('properties.currency')})
                    </label>
                    <input
                      type="number"
                      value={filters.priceRange.max}
                      onChange={(e) => handlePriceChange('max', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-italian-green focus:border-italian-green dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
              </div>

              {/* Location */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  {t('properties.filters.location.title')}
                </h3>
                <input
                  type="text"
                  value={filters.location}
                  onChange={(e) => handleLocationChange(e.target.value)}
                  placeholder={t('properties.filters.location.placeholder')}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-italian-green focus:border-italian-green dark:bg-gray-700 dark:text-white"
                />
              </div>

              {/* Property Type */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  {t('properties.filters.propertyType.title')}
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {propertyTypes.map((type) => (
                    <motion.button
                      key={type}
                      onClick={() => handlePropertyTypeChange(type)}
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                        filters.propertyType.includes(type)
                          ? 'bg-italian-green text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      {t(`properties.filters.propertyType.${type}`)}
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Listing Type */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  {t('properties.filters.listingType.title')}
                </h3>
                <div className="grid grid-cols-3 gap-3">
                  {(['all', 'rent', 'sale'] as const).map((type) => (
                    <motion.button
                      key={type}
                      onClick={() => handleListingTypeChange(type)}
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                        filters.listingType === type
                          ? 'bg-italian-green text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      {t(`properties.filters.listingType.${type}`)}
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Reset Button */}
              <motion.button
                onClick={handleReset}
                className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {t('properties.filters.reset')}
              </motion.button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PropertyFilters;
