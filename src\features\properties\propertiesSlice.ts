import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Property {
  id: string;
  type: 'studio' | 'apartment' | 'office';
  listingType: 'rent' | 'sale';
  location: {
    city: string;
    area: string;
  };
  size: number;
  price: number;
  images: string[];
  translations: {
    en: {
      title: string;
      description: string;
      features: string[];
    };
    fr: {
      title: string;
      description: string;
      features: string[];
    };
  };
}

export interface PropertiesState {
  items: Property[];
  loading: boolean;
  error: string | null;
}

const initialState: PropertiesState = {
  items: [
    {
      id: 'studio-saray',
      type: 'studio',
      listingType: 'rent',
      location: {
        city: 'Casablanca',
        area: 'Saray Project'
      },
      size: 50,
      price: 850000,
      images: ['/images/prop1.jpg'],
      translations: {
        en: {
          title: 'Modern Studio in Saray Project',
          description: 'Beautiful studio apartment in the heart of Casablanca, featuring modern amenities and a prime location.',
          features: ['Central location', 'Modern design', 'Security 24/7', 'Parking available']
        },
        fr: {
          title: 'Studio Moderne au Projet Saray',
          description: 'Beau studio au cœur de Casablanca, avec des équipements modernes et un emplacement privilégié.',
          features: ['Emplacement central', 'Design moderne', 'Sécurité 24/7', 'Parking disponible']
        }
      }
    },
    {
      id: 'apartment-cfc',
      type: 'apartment',
      listingType: 'sale',
      location: {
        city: 'Casablanca',
        area: 'CFC (Casa Finance City)'
      },
      size: 70,
      price: 1500000,
      images: ['/images/prop2.jpg'],
      translations: {
        en: {
          title: 'Luxury Apartment in CFC',
          description: 'Spacious apartment in the prestigious Casa Finance City, offering premium living space and excellent connectivity.',
          features: ['Premium location', 'High-end finishes', 'Building amenities', 'Underground parking']
        },
        fr: {
          title: 'Appartement de Luxe à CFC',
          description: 'Appartement spacieux dans la prestigieuse Casa Finance City, offrant un espace de vie premium et une excellente connectivité.',
          features: ['Emplacement premium', 'Finitions haut de gamme', 'Services inclus', 'Parking souterrain']
        }
      }
    },
    {
      id: 'office-cfc',
      type: 'office',
      listingType: 'sale',
      location: {
        city: 'Casablanca',
        area: 'CFC (Casa Finance City)'
      },
      size: 120,
      price: 2400000,
      images: ['/images/prop3.jpg'],
      translations: {
        en: {
          title: 'Premium Office Space in CFC',
          description: 'Large office space in Casa Finance City, perfect for businesses looking for a prestigious address.',
          features: ['Business district', 'Modern infrastructure', 'Meeting rooms', 'Reception service']
        },
        fr: {
          title: 'Bureau Premium à CFC',
          description: 'Grand espace de bureau à Casa Finance City, parfait pour les entreprises recherchant une adresse prestigieuse.',
          features: ['Quartier des affaires', 'Infrastructure moderne', 'Salles de réunion', 'Service de réception']
        }
      }
    }
  ],
  loading: false,
  error: null
};

const propertiesSlice = createSlice({
  name: 'properties',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    filterProperties: (_state, _action: PayloadAction<{
      type?: string;
      minPrice?: number;
      maxPrice?: number;
      minSize?: number;
      maxSize?: number;
      location?: string;
    }>) => {
      // Filter implementation can be added here
    }
  }
});

export const { setLoading, setError, filterProperties } = propertiesSlice.actions;
export default propertiesSlice.reducer;
