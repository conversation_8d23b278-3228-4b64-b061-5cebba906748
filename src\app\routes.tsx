import React from 'react';
import { Route, Routes } from 'react-router-dom';
import Home from '../features/home/<USER>/Home';
import CFCPage from '../features/locations/pages/CFCPage';
import SarayPage from '../features/locations/pages/SarayPage';
import Properties from '../features/properties/components/Properties';
import PropertyDetail from '../features/properties/components/PropertyDetail';
import Services from '../features/services/components/Services';
import About from '../features/about/components/About';
import Contact from '../features/contact/components/Contact';
import Career from '../features/career/components/Career';
import { Terms } from '../features/terms/components/Terms';
import { Privacy } from '../features/privacy/components/Privacy';

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<Home />} />
      <Route path="/properties" element={<Properties />} />
      <Route path="/properties/:id" element={<PropertyDetail />} />
      <Route path="/services" element={<Services />} />
      <Route path="/about" element={<About />} />
      <Route path="/contact" element={<Contact />} />
      <Route path="/career" element={<Career />} />
      <Route path="/terms" element={<Terms />} />
      <Route path="/privacy" element={<Privacy />} />
      <Route path="/locations/cfc" element={<CFCPage />} />
      <Route path="/locations/saray" element={<SarayPage />} />
    </Routes>
  );
};

export default AppRoutes;
