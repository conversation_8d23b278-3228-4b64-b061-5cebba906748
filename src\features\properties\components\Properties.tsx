import React, { useMemo, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import type { RootState } from '../../../app/store';
import TypewriterText from '../../contact/components/TypewriterText';
import PropertyFilters from './PropertyFilters';

const Properties: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const properties = useSelector((state: RootState) => state.properties.items);
  const filters = useSelector((state: RootState) => state.filters);
  const [loadingPropertyId, setLoadingPropertyId] = useState<string | null>(null);

  const handlePropertyClick = (e: React.MouseEvent, propertyId: string) => {
    e.preventDefault();
    setLoadingPropertyId(propertyId);
    setTimeout(() => {
      navigate(`/properties/${propertyId}`);
    }, 300);
  };

  const filteredProperties = useMemo(() => {
    return properties.filter((property) => {
      // Filter by price range
      const price = property.price;
      if (price < filters.priceRange.min || price > filters.priceRange.max) {
        return false;
      }

      // Filter by location
      if (filters.location && !(`${property.location.city} ${property.location.area}`).toLowerCase().includes(filters.location.toLowerCase())) {
        return false;
      }

      // Filter by property type
      if (filters.propertyType.length > 0 && !filters.propertyType.includes(property.type)) {
        return false;
      }

      // Filter by listing type
      if (filters.listingType !== 'all' && property.listingType !== filters.listingType) {
        return false;
      }

      return true;
    });
  }, [properties, filters]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <div className="dark:bg-gradient-to-br dark:from-gray-800/50 dark:to-gray-900/50 min-h-screen py-16 backdrop-blur-sm">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold text-theme-primary mb-4">
            <TypewriterText text={t('properties.title')} delay={70} />
          </h1>
        </motion.div>

        {/* Filters */}
        <PropertyFilters />

        {/* Properties Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {filteredProperties.map((property) => {
            const translation = property.translations[i18n.language as 'en' | 'fr'];

            return (
              <motion.div
                key={property.id}
                className="bg-theme-light dark:bg-gradient-to-br dark:from-gray-800/40 dark:to-gray-900/40 rounded-lg shadow-lg overflow-hidden group backdrop-blur-sm"
                variants={itemVariants}
                whileHover={{ y: -5, scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                {/* Property Image */}
                <div className="relative aspect-w-16 aspect-h-9 overflow-hidden">
                  <motion.img
                    src={property.images[0]}
                    alt={translation.title}
                    className="w-full h-full object-cover"
                    whileHover={{ scale: 1.1 }}
                    transition={{ duration: 0.3 }}
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-10 group-hover:bg-opacity-0 transition-opacity dark:bg-opacity-30 dark:group-hover:bg-opacity-20" />
                </div>

                {/* Property Info */}
                <div className="p-6 space-y-4">
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    <h3 className="text-xl font-semibold text-theme-primary">
                      <TypewriterText text={translation.title} delay={30} />
                    </h3>
                    <p className="text-theme-secondary">
                      {property.location.area}, {property.location.city}
                    </p>
                  </motion.div>

                  <motion.div
                    className="flex items-center justify-between"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <motion.span 
                      className="text-2xl font-bold text-italian-green"
                      whileHover={{ scale: 1.05 }}
                    >
                      ${property.price.toLocaleString()}
                    </motion.span>
                    <span className="text-sm text-theme-secondary">
                      {property.size} m²
                    </span>
                  </motion.div>

                  <div className="flex justify-between items-center">
                    <motion.span 
                      className="text-sm text-theme-secondary"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4 }}
                    >
                      {t(`properties.types.${property.type}`)}
                    </motion.span>
                    <Link to={`/properties/${property.id}`}>
                      <motion.button
                        className="btn btn-primary"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5 }}
                      >
                        {t('properties.viewDetails')}
                      </motion.button>
                    </Link>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        {filteredProperties.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center text-gray-600 dark:text-gray-300 mt-8"
          >
            {t('properties.noResults')}
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default Properties;
