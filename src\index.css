@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-primary: #008C45;
  --color-secondary: #CD212A;
  --color-neutral: #F1F5F9;
  --gradient-primary: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
  --gradient-secondary: linear-gradient(135deg, var(--color-secondary), var(--color-secondary-light));
  --gradient-accent: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
}

@layer base {
  :root {
    /* Base Colors */
    --color-primary: #008C45;
    --color-secondary: #CD212A;
    --color-neutral: #F4F5F0;
    --color-background-light: #FFFFFF;
    --color-background-dark: #1A202C;
    --color-text-primary: #1A202C;
    --color-text-secondary: #4A5568;
    --color-text-light: #FFFFFF;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--color-primary), #00B358);
    --gradient-secondary: linear-gradient(135deg, var(--color-secondary), #E63E47);
    --gradient-accent: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    --gradient-surface: linear-gradient(135deg, var(--color-neutral), var(--color-background-light));
  }

  .dark {
    --color-primary: #00B358;
    --color-secondary: #E63E47;
    --color-neutral: #2D3748;
    --color-background-light: #2D3748;
    --color-background-dark: #1A202C;
    --color-text-primary: #FFFFFF;
    --color-text-secondary: #A0AEC0;
    --color-text-light: #F7FAFC;

    --gradient-primary: linear-gradient(135deg, #006633, var(--color-primary));
    --gradient-secondary: linear-gradient(135deg, #B31B23, var(--color-secondary));
    --gradient-accent: linear-gradient(135deg, #006633, #B31B23);
    --gradient-surface: linear-gradient(135deg, var(--color-neutral), var(--color-background-dark));
  }
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Button Components */
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-200;
  }

  .btn-primary {
    @apply bg-italian-green text-white hover:bg-italian-green-light;
  }

  .btn-secondary {
    @apply bg-italian-red text-white hover:bg-italian-red-light;
  }

  /* Container Component */
  .container-custom {
    @apply container mx-auto px-4;
  }

  /* Gradient Text Component */
  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-accent;
  }

  /* Hover Effect Components */
  .gradient-hover {
    @apply relative overflow-hidden;
  }

  .gradient-hover::after {
    content: '';
    @apply absolute inset-0 opacity-0 transition-opacity duration-300 bg-gradient-primary;
  }

  .gradient-hover:hover::after {
    @apply opacity-10;
  }
}

@layer utilities {
  .bg-theme-light {
    @apply bg-white dark:bg-gray-800;
  }

  .bg-theme-dark {
    @apply bg-gray-100 dark:bg-gray-900;
  }

  .text-theme-primary {
    @apply text-gray-900 dark:text-white;
  }

  .text-theme-secondary {
    @apply text-gray-600 dark:text-gray-300;
  }

  .border-theme {
    @apply border-gray-200 dark:border-gray-700;
  }
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}