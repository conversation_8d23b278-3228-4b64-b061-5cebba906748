import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from '@/core/i18n/useTranslation';

interface Position {
  title: string;
  type: 'Internship' | 'Full-time';
  duration?: string;
  tasks: string[];
  requirements: string[];
}

const Career: React.FC = () => {
  const { t } = useTranslation();
  const positions = (t('career.positions', { returnObjects: true }) ?? []) as Position[];

  return (
    <div className="bg-gray-50 dark:bg-gray-900 min-h-screen py-16">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">{t('career.title')}</h1>
          <p className="text-xl text-gray-600 dark:text-gray-300">{t('career.subtitle')}</p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {Array.isArray(positions) && positions.map((position, index) => (
            <motion.div
              key={position.title}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{position.title}</h2>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    position.type === 'Internship' 
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                      : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  }`}>
                    {position.type}
                  </span>
                </div>

                {position.duration && (
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Duration: {position.duration}
                  </p>
                )}

                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">{t('career.responsibilities')}</h3>
                  <ul className="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
                    {position.tasks.map((task: string, taskIndex: number) => (
                      <li key={taskIndex}>{task}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">{t('career.requirements')}</h3>
                  <ul className="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
                    {position.requirements.map((requirement: string, reqIndex: number) => (
                      <li key={reqIndex}>{requirement}</li>
                    ))}
                  </ul>
                </div>

                <motion.button
                  className="mt-6 w-full bg-italian-green text-white py-2 px-4 rounded-md hover:bg-italian-green-dark transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {t('career.apply')}
                </motion.button>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Career;
