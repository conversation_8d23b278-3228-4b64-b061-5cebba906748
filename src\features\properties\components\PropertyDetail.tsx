import React from 'react';
import { useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import PropertyMap from '../../shared/components/PropertyMap';
import VirtualTour from '../../shared/components/VirtualTour';
import type { RootState } from '../../../app/store';
import type { Property } from '../propertiesSlice';

const PropertyDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { t, i18n } = useTranslation();
  const property = useSelector((state: RootState) => 
    state.properties.items.find((p: Property) => p.id === id)
  );

  if (!property) {
    return (
      <div className="min-h-screen bg-theme-dark py-12">
        <div className="container-custom">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t('properties.notFound')}
          </h1>
        </div>
      </div>
    );
  }

  const { translations, type, location, size, price, images, listingType } = property;
  const currentTranslation = translations[i18n.language as 'en' | 'fr'];

  return (
    <div className="min-h-screen bg-theme-dark py-12">
      <div className="container-custom">
        <div className="max-w-4xl mx-auto">
          {/* Image Gallery */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <div className="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden relative">
              <img
                src={images[0]}
                alt={currentTranslation.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
            </div>
          </motion.div>

          {/* Property Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-theme-light rounded-lg shadow-lg p-8"
          >
            <div className="flex flex-wrap items-start justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold text-theme-primary mb-2">
                  {currentTranslation.title}
                </h1>
                <p className="text-lg text-theme-secondary">
                  {location.area}, {location.city}
                </p>
              </div>
              <div className="text-right">
                <div className="mt-4 flex items-center justify-between">
                  <span className="text-3xl font-bold text-theme-primary">
                    {price.toLocaleString()} {t('properties.currency')}
                  </span>
                  <span className={`px-4 py-2 rounded-full text-sm font-medium ${
                    listingType === 'rent'
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                      : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  }`}>
                    {listingType === 'rent' ? t('properties.forRent') : t('properties.forSale')}
                  </span>
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {size} m² • {t(`properties.types.${type}`)}
                </p>
              </div>
            </div>

            <div className="prose dark:prose-invert max-w-none mb-8 text-theme-secondary">
              <p>{currentTranslation.description}</p>
            </div>

            {/* Features */}
            <div className="border-t border-theme pt-8">
              <h2 className="text-xl font-semibold text-theme-primary mb-4">
                {t('properties.features')}
              </h2>
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {currentTranslation.features.map((feature: string, index: number) => (
                  <li
                    key={index}
                    className="flex items-center text-theme-secondary"
                  >
                    <svg
                      className="w-5 h-5 text-theme-primary mr-3"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact Section */}
            <div className="border-t border-theme pt-8 mt-8">
              <h2 className="text-xl font-semibold text-theme-primary mb-4">
                {t('properties.interested')}
              </h2>
              <div className="flex flex-wrap gap-4">
                <button className="btn btn-primary hover:shadow-lg">
                  {t('properties.schedule')}
                </button>
                <button className="btn btn-secondary hover:shadow-lg">
                  {t('properties.contact')}
                </button>
              </div>
            </div>
          </motion.div>

          {/* Property Map */}
          <div className="mt-8">
            <h2 className="text-xl font-semibold text-theme-primary mb-4">
              Location
            </h2>
            <div className="h-[400px] rounded-lg overflow-hidden bg-theme-light">
              <PropertyMap />
            </div>
          </div>

          {/* Virtual Tour */}
          <div className="mt-8">
            <h2 className="text-xl font-semibold text-theme-primary mb-4">
              Virtual Tour
            </h2>
            <div className="h-[400px] rounded-lg overflow-hidden bg-theme-light">
              <VirtualTour />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PropertyDetail;