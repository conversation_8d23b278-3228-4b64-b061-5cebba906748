{"name": "darden<PERSON>", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "test": "vitest", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@mui/material": "^6.4.5", "@react-google-maps/api": "^2.20.6", "@reduxjs/toolkit": "^2.0.0", "@types/js-cookie": "^3.0.6", "@types/styled-components": "^5.1.34", "axios": "^1.6.0", "framer-motion": "^12.4.7", "i18next": "^24.2.2", "js-cookie": "^3.0.5", "pannellum-react": "^1.2.4", "prop-types": "^15.8.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.4.1", "react-redux": "^9.0.0", "react-router-dom": "^6.4.0", "redux-persist": "^6.0.0", "styled-components": "^6.1.15", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/testing-library__jest-dom": "^5.14.9", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^26.0.0", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^3.0.6"}}