import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Container, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import LocationGallery from '../components/LocationGallery';
import LocationDetails from '../components/LocationDetails';
import LocationMap from '../components/LocationMap';

const PageContainer = styled(Container)(({ theme }) => ({
  paddingTop: theme.spacing(4),
  paddingBottom: theme.spacing(8),
}));

const Header = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  textAlign: 'center',
}));

const SarayPage: React.FC = () => {
  const { t } = useTranslation();
  const locations = t('locations.items', { returnObjects: true }) as any[];
  const locationData = locations.find(
    (location: any) => location.id === 'saray'
  );

  if (!locationData) {
    return null;
  }

  return (
    <PageContainer maxWidth="lg">
      <Header>
        <Typography
          variant="h2"
          component="h1"
          color="primary"
          gutterBottom
        >
          {locationData.name}
        </Typography>
      </Header>

      <LocationGallery
        images={locationData.images}
      />

      <LocationDetails
        location={{
          name: locationData.name,
          description: locationData.description,
          features: locationData.features.map((feature: string, index: number) => ({
            id: index + 1,
            name: feature
          }))
        }}
      />

      <LocationMap
        address={locationData.address}
        coordinates={locationData.coordinates}
      />
    </PageContainer>
  );
};

export default SarayPage;
