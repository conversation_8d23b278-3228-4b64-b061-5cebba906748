import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Layout from './core/layout/Layout';
import AppRoutes from './app/routes';
import './App.css';

const App: React.FC = () => {
  return (
    <Layout>
      <AnimatePresence mode="wait">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <AppRoutes />
        </motion.div>
      </AnimatePresence>
    </Layout>
  );
};

export default App;
