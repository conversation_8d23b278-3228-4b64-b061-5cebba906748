import React from 'react';
import { useTranslation } from '../../../core/i18n/useTranslation';

interface PrivacySection {
  title: string;
  content: string;
}

export const Privacy: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="container-custom py-12">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          {t('privacy.title')}
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-8">
          {t('privacy.lastUpdated')}
        </p>
        
        {(t('privacy.sections', { returnObjects: true }) as PrivacySection[]).map((section: PrivacySection, index: number) => (
          <div key={index} className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
              {section.title}
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              {section.content}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};
