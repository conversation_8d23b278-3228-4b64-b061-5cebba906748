import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { propertyApi } from '../features/properties/api/propertyApi';
import themeReducer from '../core/theme/themeSlice';
import languageReducer from '../core/i18n/languageSlice';
import navigationReducer from '../core/navigation/navigationSlice';
import propertiesReducer from '../features/properties/propertiesSlice';
import filtersReducer from '../features/properties/filtersSlice';
import userPreferencesReducer from '../features/properties/userPreferencesSlice';

const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['userPreferences', 'theme', 'language'],
};

const rootReducer = combineReducers({
  [propertyApi.reducerPath]: propertyApi.reducer,
  theme: persistReducer(persistConfig, themeReducer),
  language: persistReducer(persistConfig, languageReducer),
  navigation: navigationReducer,
  properties: propertiesReducer,
  filters: filtersReducer,
  userPreferences: persistReducer(persistConfig, userPreferencesReducer),
});

const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }).concat(propertyApi.middleware),
});

export const persistor = persistStore(store);
export type AppDispatch = typeof store.dispatch;
export type RootState = ReturnType<typeof store.getState>;
export default store;
