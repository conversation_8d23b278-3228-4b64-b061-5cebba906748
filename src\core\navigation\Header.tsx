import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { toggleTheme } from '../theme/themeSlice';
import { changeLanguage } from '../i18n/languageSlice';
import { toggleMenu } from './navigationSlice';
import type { RootState } from '../../app/store';
import useTranslation from '../i18n/useTranslation';

const Header: React.FC = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const { mode } = useSelector((state: RootState) => state.theme);
  const { currentLanguage: lang } = useSelector((state: RootState) => state.language);
  const { isMenuOpen } = useSelector((state: RootState) => state.navigation);
  const { t } = useTranslation();

  const handleThemeToggle = () => {
    dispatch(toggleTheme());
  };

  const handleLanguageChange = (selectedLang: 'en' | 'fr') => {
    dispatch(changeLanguage(selectedLang));
  };

  const handleMenuToggle = () => {
    dispatch(toggleMenu());
  };

  const isActive = (path: string) => location.pathname === path;

  return (
    <header className="bg-theme-light shadow-md relative border-theme">
      <div className="absolute inset-0 gradient-hover" />
      <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-gradient-accent" />
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link to="/" className="flex items-center">
              <img 
                src="logo.png" 
                alt="Darden PM" 
                className="h-8 w-auto"
              />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-4">
            <Link 
              to="/" 
              className={`nav-link relative px-3 py-2 text-theme-secondary hover:text-theme-primary gradient-hover
                ${isActive('/') ? 'text-theme-primary font-semibold' : ''}`}
            >
              {t('navigation.home')}
            </Link>
            <Link 
              to="/properties" 
              className={`nav-link relative px-3 py-2 text-gray-700 dark:text-gray-200 hover:text-italian-green
                ${isActive('/properties') ? 'before:absolute before:bottom-0 before:left-0 before:right-0 before:h-[2px] before:bg-gradient-to-r before:from-italian-green before:via-white before:to-italian-red' : ''}`}
              style={{ backgroundSize: '100% 100%' }}
            >
              {t('navigation.properties')}
            </Link>
            <Link 
              to="/services" 
              className={`nav-link relative px-3 py-2 text-gray-700 dark:text-gray-200 hover:text-italian-green
                ${isActive('/services') ? 'before:absolute before:bottom-0 before:left-0 before:right-0 before:h-[2px] before:bg-gradient-to-r before:from-italian-green before:via-white before:to-italian-red' : ''}`}
              style={{ backgroundSize: '100% 100%' }}
            >
              {t('navigation.services')}
            </Link>
            <Link 
              to="/about" 
              className={`nav-link relative px-3 py-2 text-gray-700 dark:text-gray-200 hover:text-italian-green
                ${isActive('/about') ? 'before:absolute before:bottom-0 before:left-0 before:right-0 before:h-[2px] before:bg-gradient-to-r before:from-italian-green before:via-white before:to-italian-red' : ''}`}
              style={{ backgroundSize: '100% 100%' }}
            >
              {t('navigation.about')}
            </Link>
            <Link 
              to="/career" 
              className={`nav-link relative px-3 py-2 text-gray-700 dark:text-gray-200 hover:text-italian-green
                ${isActive('/career') ? 'before:absolute before:bottom-0 before:left-0 before:right-0 before:h-[2px] before:bg-gradient-to-r before:from-italian-green before:via-white before:to-italian-red' : ''}`}
              style={{ backgroundSize: '100% 100%' }}
            >
              {t('navigation.career')}
            </Link>
            <Link 
              to="/contact" 
              className={`nav-link relative px-3 py-2 text-gray-700 dark:text-gray-200 hover:text-italian-green
                ${isActive('/contact') ? 'before:absolute before:bottom-0 before:left-0 before:right-0 before:h-[2px] before:bg-gradient-to-r before:from-italian-green before:via-white before:to-italian-red' : ''}`}
              style={{ backgroundSize: '100% 100%' }}
            >
              {t('common.contact')}
            </Link>
          </nav>

          {/* Controls */}
          <div className="hidden md:flex items-center space-x-4 relative z-50">
            {/* Language Toggle */}
            <div className="flex space-x-2 relative">
              <button
                type="button"
                onClick={() => handleLanguageChange('en')}
                className={`px-2 py-1 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-theme-light
                  ${lang === 'en' 
                    ? 'bg-theme-light text-theme-primary' 
                    : 'text-theme-secondary hover:text-theme-primary'
                  }`}
              >
                EN
              </button>
              <button
                type="button"
                onClick={() => handleLanguageChange('fr')}
                className={`px-2 py-1 rounded-md transition-colors cursor-pointer select-none focus:outline-none focus:ring-2 focus:ring-italian-green
                  ${lang === 'fr' 
                    ? 'bg-italian-green dark:bg-italian-green-light text-white' 
                    : 'text-gray-500 dark:text-gray-400 hover:text-italian-green dark:hover:text-italian-green-light'
                  }`}
              >
                FR
              </button>
            </div>

            {/* Theme Toggle */}
            <button
              onClick={handleThemeToggle}
              className="p-2 text-theme-secondary hover:text-theme-primary transition-all duration-200"
              aria-label="Toggle theme"
            >
              {mode === 'light' ? '🌙' : '☀️'}
            </button>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden relative z-50">
            <button
              onClick={handleMenuToggle}
              className="p-2 text-gray-500 dark:text-gray-400 hover:text-italian-green dark:hover:text-italian-green-light
                rounded-md focus:outline-none focus:ring-2 focus:ring-italian-green dark:focus:ring-italian-green-light cursor-pointer"
              aria-expanded={isMenuOpen}
              type="button"
            >
              <span className="sr-only">Open main menu</span>
              {isMenuOpen ? (
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : (
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-theme-light absolute top-16 left-0 right-0 z-40 shadow-lg">
            <div className="px-2 pt-2 pb-3 space-y-1 border-theme">
              <Link
                to="/"
                className={`block px-3 py-2 rounded-md text-gray-700 dark:text-gray-200 
                  ${isActive('/') ? 'bg-italian-green/10 dark:bg-italian-green-light/10 text-italian-green dark:text-italian-green-light' : ''}`}
                onClick={handleMenuToggle}
                style={{ backgroundSize: '100% 100%' }}
              >
                {t('navigation.home')}
              </Link>
              <Link
                to="/properties"
                className={`block px-3 py-2 rounded-md text-gray-700 dark:text-gray-200
                  ${isActive('/properties') ? 'bg-italian-green/10 dark:bg-italian-green-light/10 text-italian-green dark:text-italian-green-light' : ''}`}
                onClick={handleMenuToggle}
                style={{ backgroundSize: '100% 100%' }}
              >
                {t('navigation.properties')}
              </Link>
              <Link
                to="/services"
                className={`block px-3 py-2 rounded-md text-gray-700 dark:text-gray-200
                  ${isActive('/services') ? 'bg-italian-green/10 dark:bg-italian-green-light/10 text-italian-green dark:text-italian-green-light' : ''}`}
                onClick={handleMenuToggle}
                style={{ backgroundSize: '100% 100%' }}
              >
                {t('navigation.services')}
              </Link>
              <Link
                to="/about"
                className={`block px-3 py-2 rounded-md text-gray-700 dark:text-gray-200
                  ${isActive('/about') ? 'bg-italian-green/10 dark:bg-italian-green-light/10 text-italian-green dark:text-italian-green-light' : ''}`}
                onClick={handleMenuToggle}
                style={{ backgroundSize: '100% 100%' }}
              >
                {t('navigation.about')}
              </Link>
              <Link
                to="/career"
                className={`block px-3 py-2 rounded-md text-gray-700 dark:text-gray-200
                  ${isActive('/career') ? 'bg-italian-green/10 dark:bg-italian-green-light/10 text-italian-green dark:text-italian-green-light' : ''}`}
                onClick={handleMenuToggle}
                style={{ backgroundSize: '100% 100%' }}
              >
                {t('navigation.career')}
              </Link>
              <Link
                to="/contact"
                className={`block px-3 py-2 rounded-md text-gray-700 dark:text-gray-200
                  ${isActive('/contact') ? 'bg-italian-green/10 dark:bg-italian-green-light/10 text-italian-green dark:text-italian-green-light' : ''}`}
                onClick={handleMenuToggle}
                style={{ backgroundSize: '100% 100%' }}
              >
                {t('common.contact')}
              </Link>
              
              {/* Mobile Controls */}
              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-800">
                <div className="flex justify-between items-center px-3">
                  <div className="flex space-x-2">
                    <button
                      type="button"
                      onClick={() => handleLanguageChange('en')}
                      className={`px-2 py-1 rounded-md transition-colors cursor-pointer select-none focus:outline-none focus:ring-2 focus:ring-italian-green
                        ${lang === 'en' 
                          ? 'bg-italian-green dark:bg-italian-green-light text-white' 
                          : 'text-gray-500 dark:text-gray-400 hover:text-italian-green dark:hover:text-italian-green-light'
                        }`}
                    >
                      EN
                    </button>
                    <button
                      type="button"
                      onClick={() => handleLanguageChange('fr')}
                      className={`px-2 py-1 rounded-md transition-colors cursor-pointer select-none focus:outline-none focus:ring-2 focus:ring-italian-green
                        ${lang === 'fr' 
                          ? 'bg-italian-green dark:bg-italian-green-light text-white' 
                          : 'text-gray-500 dark:text-gray-400 hover:text-italian-green dark:hover:text-italian-green-light'
                        }`}
                    >
                      FR
                    </button>
                  </div>
                  <button
                    onClick={handleThemeToggle}
                    className="p-2 text-gray-500 dark:text-gray-400 hover:text-italian-green dark:hover:text-italian-green-light transition-colors"
                    aria-label="Toggle theme"
                    style={{ backgroundSize: '100% 100%' }}
                  >
                    {mode === 'light' ? '🌙' : '☀️'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;