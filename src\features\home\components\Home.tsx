import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import type { RootState } from '../../../app/store';
import type { Property } from '../../properties/propertiesSlice';
import TypewriterText from '../../contact/components/TypewriterText';
import LocationHighlights from './LocationHighlights';

interface Service {
  icon: string;
  title: string;
  description: string;
}

const Home: React.FC = () => {
  const { t, i18n } = useTranslation();
  const properties = useSelector((state: RootState) => state.properties.items);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Mouse position effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Animation variants
  const titleRevealAnimation = {
    initial: { 
      opacity: 0,
      clipPath: "polygon(0 0, 0 0, 0 100%, 0% 100%)"
    },
    animate: { 
      opacity: 1,
      clipPath: "polygon(0 0, 100% 0, 100% 100%, 0 100%)",
      transition: {
        duration: 0.5,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const floatingAnimation = {
    y: [0, -10, 0],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut"
    }
  };

  const services: Service[] = [
    {
      icon: "🏢",
      title: t('services.propertyManagement.title'),
      description: t('services.propertyManagement.description'),
    },
    {
      icon: "📊",
      title: t('services.consulting.title'),
      description: t('services.consulting.description'),
    },
    {
      icon: "🔑",
      title: t('services.rental.title'),
      description: t('services.rental.description'),
    },
  ];

  const stats = t('about.stats.items', { returnObjects: true }) as Array<{ value: string; label: string }>;

  return (
    <div className="bg-gray-50 dark:bg-gradient-to-br dark:from-gray-800/50 dark:to-gray-900/50 overflow-hidden backdrop-blur-sm">
      {/* Hero Section */}
      <section className="relative min-h-[600px] flex items-center overflow-hidden">
        <motion.div
          className="absolute inset-0"
          style={{
            background: 'radial-gradient(circle at var(--x) var(--y), transparent 30%, var(--color-primary) 100%)',
            backgroundSize: '200% 200%',
          }}
          animate={{
            '--x': `${mousePosition.x}px`,
            '--y': `${mousePosition.y}px`,
          } as any}
        />

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            className="text-center max-w-3xl mx-auto"
            initial={{ opacity: 0, scale: 0.5, y: 100 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            transition={{ duration: 1, type: "spring", bounce: 0.5 }}
          >
            <motion.h1 
              className="text-6xl md:text-7xl font-bold text-gray-900 dark:text-white mb-6 relative"
              initial={titleRevealAnimation.initial}
              animate={titleRevealAnimation.animate}
            >
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-italian-green/20 to-transparent"
                animate={{
                  opacity: [0, 0.5, 0],
                  x: ['-100%', '100%'],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />
              <TypewriterText text={t('home.hero.title')} delay={70} />
            </motion.h1>
            <motion.p 
              className="text-xl text-gray-600 dark:text-gray-300 mb-12"
              animate={floatingAnimation}
            >
              <TypewriterText text={t('home.hero.subtitle')} delay={50} />
            </motion.p>
            <motion.div
              whileHover={{ 
                scale: 1.05,
                transition: { duration: 0.3 }
              }}
              whileTap={{ scale: 0.95 }}
              drag
              dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
              dragElastic={0.1}
            >
              <Link
                to="/properties"
                className="inline-block bg-italian-green text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-italian-green/90 transition-colors transform-gpu"
              >
                {t('home.hero.cta')}
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Featured Properties */}
      <section className="py-16 bg-white dark:bg-gradient-to-br dark:from-gray-800/40 dark:to-gray-900/40 backdrop-blur-sm">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              {t('home.featured.title')}
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {properties.slice(0, 3).map((property: Property, index: number) => (
              <motion.div
                key={property.id}
                className="bg-white dark:bg-gradient-to-br dark:from-gray-800/30 dark:to-gray-900/30 rounded-xl shadow-lg overflow-hidden group cursor-pointer backdrop-blur-sm"
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.2 }}
                whileHover={{ 
                  scale: 1.05,
                  rotateY: [-5, 5],
                  transition: { duration: 0.3 }
                }}
                style={{ perspective: "1000px" }}
              >
                <div className="relative h-48 overflow-hidden">
                  <motion.div 
                    className="absolute inset-0 bg-gradient-to-r from-italian-green/20 to-transparent"
                    animate={{
                      x: ['-100%', '100%'],
                      scale: [1, 1.2, 1],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: index * 0.5
                    }}
                  />
                  <motion.img
                    src={property.images[0]}
                    alt={property.translations[i18n.language as 'en' | 'fr'].title}
                    className="w-full h-full object-cover"
                    whileHover={{ scale: 1.2 }}
                    transition={{ duration: 0.5 }}
                  />
                  <motion.div
                    className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 flex items-center justify-center"
                    initial={false}
                    transition={{ duration: 0.3 }}
                  >
                    <motion.span
                      className="text-white text-lg font-semibold"
                      initial={{ y: 20, opacity: 0 }}
                      whileHover={{ y: 0, opacity: 1 }}
                    >
                      {t('properties.viewDetails')}
                    </motion.span>
                  </motion.div>
                </div>
                <motion.div 
                  className="p-6"
                  whileHover={{ backgroundColor: "rgba(34, 197, 94, 0.1)" }}
                >
                  <motion.h3 
                    className="text-xl font-semibold text-gray-900 dark:text-white mb-2"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ 
                      opacity: 1, 
                      y: 0,
                      transition: {
                        duration: 0.8,
                        ease: [0.22, 1, 0.36, 1]
                      }
                    }}
                    viewport={{ once: true }}
                  >
                    {property.translations[i18n.language as 'en' | 'fr'].title}
                  </motion.h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    {property.location.city}, {property.location.area}
                  </p>
                  <motion.div
                    className="flex justify-between items-center"
                    whileHover={{ x: 10 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <span className="text-italian-green font-bold">
                      {property.price.toLocaleString()} {t('properties.currency')}
                    </span>
                    <Link
                      to={`/properties/${property.id}`}
                      className="text-italian-green hover:text-italian-green/80 font-medium inline-flex items-center"
                    >
                      {t('home.featured.viewAll')}
                      <motion.span
                        className="ml-2"
                        animate={{ x: [0, 5, 0] }}
                        transition={{
                          duration: 1,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      >
                        →
                      </motion.span>
                    </Link>
                  </motion.div>
                </motion.div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Location Highlights Section */}
      <LocationHighlights />

      {/* Services Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              {t('services.title')}
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              {t('services.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {services.map((service: Service, index: number) => (
              <motion.div
                key={index}
                className="bg-white dark:bg-gradient-to-br dark:from-gray-800/30 dark:to-gray-900/30 p-8 rounded-xl shadow-lg transform-gpu backdrop-blur-sm"
                style={{ perspective: "1000px" }}
                initial={{ opacity: 0, rotateY: 90 }}
                whileInView={{ opacity: 1, rotateY: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                whileHover={{ 
                  scale: 1.05,
                  rotateY: [-10, 10, 0],
                  transition: { duration: 0.5 }
                }}
              >
                <motion.div 
                  className="text-4xl mb-4"
                  animate={{ 
                    rotate: [0, 360],
                    scale: [1, 1.2, 1]
                  }}
                  transition={{ 
                    duration: 20,
                    repeat: Infinity,
                    ease: "linear",
                    scale: {
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }
                  }}
                >
                  {service.icon}
                </motion.div>
                <motion.h3 
                  className="text-xl font-semibold text-gray-900 dark:text-white mb-4"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ 
                    opacity: 1, 
                    y: 0,
                    transition: {
                      duration: 0.8,
                      ease: [0.22, 1, 0.36, 1]
                    }
                  }}
                  viewport={{ once: true }}
                >
                  {service.title}
                </motion.h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {service.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-italian-green text-white relative overflow-hidden">
        <motion.div
          className="absolute inset-0"
          style={{
            background: 'radial-gradient(circle at center, transparent 30%, rgba(0,0,0,0.2) 100%)',
          }}
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.5, 0.8, 0.5],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <div className="container mx-auto px-4 relative z-10">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                className="text-center"
                initial={{ opacity: 0, scale: 0.5 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <motion.div 
                  className="text-4xl font-bold mb-2"
                  animate={{ 
                    scale: [1, 1.2, 1],
                    rotate: [0, 5, -5, 0],
                    textShadow: [
                      "0 0 0px rgba(255,255,255,0.5)",
                      "0 0 20px rgba(255,255,255,0.8)",
                      "0 0 0px rgba(255,255,255,0.5)"
                    ]
                  }}
                  transition={{ 
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  {stat.value}
                </motion.div>
                <motion.div 
                  className="text-sm opacity-90"
                  animate={{ 
                    y: [0, -5, 0],
                    opacity: [0.7, 1, 0.7]
                  }}
                  transition={{ 
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  {stat.label}
                </motion.div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.div
            className="bg-white dark:bg-gradient-to-br dark:from-gray-800/30 dark:to-gray-900/30 rounded-2xl p-8 md:p-12 text-center max-w-4xl mx-auto shadow-xl relative overflow-hidden backdrop-blur-sm"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            whileHover={{ 
              scale: 1.02,
              boxShadow: "0 20px 40px rgba(0,0,0,0.2)"
            }}
          >
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-italian-green/10 to-transparent"
              animate={{
                x: ['-100%', '100%'],
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.h2 
              className="text-3xl font-bold text-gray-900 dark:text-white mb-4 relative z-10"
              initial={titleRevealAnimation.initial}
              whileInView={titleRevealAnimation.animate}
              viewport={{ once: true }}
            >
              {t('common.welcome')}
            </motion.h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 relative z-10">
              {t('footer.description')}
            </p>
            <motion.div
              className="relative z-10"
              whileHover={{ 
                scale: 1.1,
                rotate: [0, -2, 2, 0],
              }}
              whileTap={{ scale: 0.9 }}
              drag
              dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
              dragElastic={0.1}
            >
              <Link
                to="/contact"
                className="inline-block bg-italian-green text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-italian-green/90 transition-colors transform-gpu"
              >
                {t('common.contact')}
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home;
