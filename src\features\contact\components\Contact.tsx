import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import TypewriterText from './TypewriterText';

const Contact: React.FC = () => {
  const { t } = useTranslation();
  const [formState, setFormState] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
  });
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formState.name || !formState.email || !formState.message) {
      setStatus('error');
      return;
    }

    // Simulate form submission
    setStatus('success');
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormState(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <div className="bg-theme-dark min-h-screen py-16">
      <div className="container-custom">
        {/* Header */}
        <motion.div
          className="text-center mb-16 text-theme-primary"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            <TypewriterText text={t('contact.title')} delay={70} />
          </h1>
          <p className="text-xl text-theme-secondary">
            <TypewriterText text={t('contact.subtitle')} delay={50} />
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-12">
          {/* Contact Form */}
          <motion.div
            className="bg-theme-light rounded-lg shadow-lg p-8"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <form onSubmit={handleSubmit} className="space-y-6 text-theme-primary">
              {[
                { name: 'name', type: 'text', label: t('contact.form.name') },
                { name: 'email', type: 'email', label: t('contact.form.email') },
                { name: 'phone', type: 'tel', label: t('contact.form.phone') },
                { name: 'subject', type: 'text', label: t('contact.form.subject') }
              ].map((field) => (
                <motion.div key={field.name} variants={itemVariants}>
                  <label 
                    htmlFor={field.name}
                    className="block text-sm font-medium text-theme-secondary mb-2"
                  >
                    {field.label}
                  </label>
                  <input
                    type={field.type}
                    id={field.name}
                    name={field.name}
                    value={formState[field.name as keyof typeof formState]}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-theme rounded-md focus:ring-italian-green focus:border-italian-green bg-theme-dark text-theme-primary"
                    required={field.name !== 'phone'}
                  />
                </motion.div>
              ))}

              <motion.div variants={itemVariants}>
                <label 
                  htmlFor="message"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                >
                  {t('contact.form.message')}
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formState.message}
                  onChange={handleChange}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-italian-green focus:border-italian-green dark:bg-gray-700 dark:text-white"
                  required
                />
              </motion.div>

              <motion.div variants={itemVariants}>
                <label 
                  htmlFor="schedule"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                >
                  {t('contact.form.schedule')}
                </label>
                <input
                  type="datetime-local"
                  id="schedule"
                  name="schedule"
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-italian-green focus:border-italian-green dark:bg-gray-700 dark:text-white"
                />
              </motion.div>

              <motion.div variants={itemVariants}>
                <label 
                  htmlFor="contactPreference"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                >
                  {t('contact.form.contactPreference')}
                </label>
                <select
                  id="contactPreference"
                  name="contactPreference"
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-italian-green focus:border-italian-green dark:bg-gray-700 dark:text-white"
                >
                  <option value="email">{t('contact.form.email')}</option>
                  <option value="phone">{t('contact.form.phone')}</option>
                </select>
              </motion.div>

              <motion.div variants={itemVariants}>
                <label 
                  htmlFor="fileUpload"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                >
                  {t('contact.form.fileUpload')}
                </label>
                <input
                  type="file"
                  id="fileUpload"
                  name="fileUpload"
                  onChange={(e) => setUploadedFile(e.target.files?.[0] || null)}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-italian-green focus:border-italian-green dark:bg-gray-700 dark:text-white"
                />
                {uploadedFile && (
                  <p className="text-sm text-gray-600 dark:text-gray-300 mt-2">
                    {uploadedFile.name}
                  </p>
                )}
              </motion.div>

              <motion.div variants={itemVariants}>
                <div className="flex space-x-4">
                  <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" className="text-gray-500 hover:text-italian-green dark:text-gray-400 dark:hover:text-italian-green">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M18.77,7.46H14.5v-1.9c0-.9.6-1.1,1-1.1h3V.5h-4.33C10.24.5,9.5,3.44,9.5,5.32v2.15h-3v4h3v12h5v-12h3.85l.42-4Z"/>
                    </svg>
                  </a>
                  <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="text-gray-500 hover:text-italian-green dark:text-gray-400 dark:hover:text-italian-green">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                    </svg>
                  </a>
                  <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" className="text-gray-500 hover:text-italian-green dark:text-gray-400 dark:hover:text-italian-green">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                  </a>
                </div>
              </motion.div>

              <motion.button
                type="submit"
                className="w-full btn btn-primary"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                variants={itemVariants}
              >
                {t('contact.form.submit')}
              </motion.button>

              {status === 'success' && (
                <motion.p
                  className="text-theme-secondary text-center mt-4"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  {t('contact.form.success')}
                </motion.p>
              )}

              {status === 'error' && (
                <motion.p
                  className="text-theme-secondary text-center mt-4"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  {t('contact.form.error')}
                </motion.p>
              )}
            </form>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            className="bg-gradient-primary text-white rounded-lg shadow-lg p-8"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <motion.h2 
              className="text-2xl font-bold mb-8"
              variants={itemVariants}
            >
              {t('contact.info.title')}
            </motion.h2>

            <div className="space-y-6 text-theme-light">
              {[
                { icon: 'M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z M15 11a3 3 0 11-6 0 3 3 0 016 0z', text: 'contact.info.address' },
                { icon: 'M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z', text: 'contact.info.email' },
                { icon: 'M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z', text: 'contact.info.phone' },
                { icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z', text: 'contact.info.hours' }
              ].map((item, index) => (
                <motion.div 
                  key={index}
                  className="flex items-center space-x-4"
                  variants={itemVariants}
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={item.icon} />
                  </svg>
                  <TypewriterText text={t(item.text)} delay={30} />
                </motion.div>
              ))}
            </div>

            {/* Map Placeholder with hover effect */}
            <motion.div 
              className="mt-8 h-48 bg-white/10 rounded-lg cursor-pointer"
              variants={itemVariants}
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            />
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Contact;