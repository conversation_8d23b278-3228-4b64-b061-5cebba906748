import { Variants } from 'framer-motion';

// Particle system configuration
export const particleConfig = {
  count: 50,
  colors: ['#4a90e2', '#50E3C2', '#E3506E'],
  speed: {
    min: 0.3,
    max: 0.7
  },
  size: {
    min: 2,
    max: 4
  },
  connectionDistance: 150,
  connectionOpacity: 0.15
};

// Background configuration
export const backgroundConfig = {
  gradient: {
    primary: 'rgba(74, 144, 226, 0.1)',
    secondary: 'rgba(80, 227, 194, 0.1)'
  },
  wave: {
    amplitude: 25,
    frequency: 0.02,
    speed: 0.5
  }
};

// Shared animation variants
export const sharedAnimations: Record<string, Variants> = {
  pageTransition: {
    initial: { 
      opacity: 0, 
      y: 20,
      transition: { duration: 0.6, ease: 'easeInOut' }
    },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6, ease: 'easeInOut' }
    },
    exit: { 
      opacity: 0, 
      y: -20,
      transition: { duration: 0.6, ease: 'easeInOut' }
    }
  },
  container: {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  },
  item: {
    hidden: { opacity: 0, y: 20 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  },
  textReveal: {
    initial: { y: '100%' },
    animate: {
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.6, 0.01, -0.05, 0.95]
      }
    }
  },
  floating: {
    initial: { y: 0 },
    animate: {
      y: [-10, 10],
      transition: {
        duration: 2,
        repeat: Infinity,
        repeatType: 'reverse',
        ease: 'easeInOut'
      }
    }
  },
  card: {
    initial: { scale: 1 },
    hover: {
      scale: 1.03,
      boxShadow: '0 10px 20px rgba(0,0,0,0.1)',
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    }
  },
  button: {
    tap: { scale: 0.95 },
    hover: { scale: 1.05 }
  },
  icon: {
    initial: { rotate: 0 },
    hover: {
      rotate: 15,
      transition: {
        duration: 0.3,
        ease: 'easeInOut'
      }
    }
  }
};

// Animation utility functions
export const animationUtils = {
  generateGradientStyle: (angle = 45) => ({
    background: `linear-gradient(${angle}deg, ${backgroundConfig.gradient.primary}, ${backgroundConfig.gradient.secondary})`
  }),

  generateGridStyle: (size = 20, color = 'rgba(255,255,255,0.1)') => ({
    backgroundImage: `linear-gradient(${color} 1px, transparent 1px), linear-gradient(90deg, ${color} 1px, transparent 1px)`,
    backgroundSize: `${size}px ${size}px`
  }),

  generateWaveStyle: (color = 'rgba(255,255,255,0.1)') => ({
    maskImage: `radial-gradient(${color} 50%, transparent 70%)`
  })
};

// Particle system utility functions
export const particleUtils = {
  createParticle: (canvas: HTMLCanvasElement) => {
    const { width, height } = canvas;
    return {
      x: Math.random() * width,
      y: Math.random() * height,
      size: Math.random() * (particleConfig.size.max - particleConfig.size.min) + particleConfig.size.min,
      speedX: (Math.random() - 0.5) * (particleConfig.speed.max - particleConfig.speed.min),
      speedY: (Math.random() - 0.5) * (particleConfig.speed.max - particleConfig.speed.min),
      color: particleConfig.colors[Math.floor(Math.random() * particleConfig.colors.length)]
    };
  },

  updateParticle: (particle: any, canvas: HTMLCanvasElement) => {
    particle.x += particle.speedX;
    particle.y += particle.speedY;

    if (particle.x < 0 || particle.x > canvas.width) particle.speedX *= -1;
    if (particle.y < 0 || particle.y > canvas.height) particle.speedY *= -1;
  },

  drawParticle: (ctx: CanvasRenderingContext2D, particle: any) => {
    ctx.beginPath();
    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
    ctx.fillStyle = particle.color;
    ctx.fill();
  },

  drawConnections: (ctx: CanvasRenderingContext2D, particles: any[]) => {
    for (let i = 0; i < particles.length; i++) {
      for (let j = i + 1; j < particles.length; j++) {
        const dx = particles[i].x - particles[j].x;
        const dy = particles[i].y - particles[j].y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < particleConfig.connectionDistance) {
          ctx.beginPath();
          ctx.strokeStyle = `rgba(255,255,255,${particleConfig.connectionOpacity * (1 - distance / particleConfig.connectionDistance)})`;
          ctx.lineWidth = 1;
          ctx.moveTo(particles[i].x, particles[i].y);
          ctx.lineTo(particles[j].x, particles[j].y);
          ctx.stroke();
        }
      }
    }
  }
};
