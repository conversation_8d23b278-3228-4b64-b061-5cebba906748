import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// English translations
const enTranslations = {
  common: {
    welcome: 'Welcome to Darden Property & Management',
    search: 'Search',
    menu: 'Menu',
    darkMode: 'Dark Mode',
    language: 'Language',
    contact: 'Contact Us',
  },
  navigation: {
    home: 'Home',
    properties: 'Properties',
    career: 'Career',
    services: 'Services',
    about: 'About',
    contact: 'Contact',
    terms: 'Terms of Service',
    privacy: 'Privacy Policy'
  },
  career: {
    title: 'Join Our Team',
    subtitle: 'Build Your Career with Darden Property & Management',
    apply: 'Apply Now',
    responsibilities: 'Key Responsibilities',
    requirements: 'Requirements',
    positions: [
      {
        title: 'Full Stack Development Intern',
        type: 'Internship',
        duration: '3-6 months',
        tasks: [
          'Assist in developing React/TypeScript frontend components',
          'Work on Node.js/Express backend services',
          'Participate in database design and implementation',
          'Write unit tests and documentation',
          'Collaborate with senior developers on feature implementation'
        ],
        requirements: [
          'Currently pursuing a degree in Computer Science or related field',
          'Basic knowledge of React and Node.js',
          'Understanding of databases (SQL/NoSQL)',
          'Good problem-solving skills',
          'Strong desire to learn and grow'
        ]
      },
      {
        title: 'Sales Intern',
        type: 'Internship',
        duration: '3-6 months',
        tasks: [
          'Assist in property showings and client meetings',
          'Support lead generation and follow-up activities',
          'Help maintain CRM database',
          'Prepare sales reports and presentations',
          'Learn about real estate market analysis'
        ],
        requirements: [
          'Currently pursuing a degree in Business, Marketing, or related field',
          'Strong communication and interpersonal skills',
          'Basic understanding of sales processes',
          'Proficiency in MS Office suite',
          'Ability to work in a fast-paced environment'
        ]
      },
      {
        title: 'Digital Marketing Intern',
        type: 'Internship',
        duration: '3-6 months',
        tasks: [
          'Assist in social media management and content creation',
          'Help with email marketing campaigns',
          'Support SEO optimization efforts',
          'Analyze marketing metrics and prepare reports',
          'Create and edit property listing content'
        ],
        requirements: [
          'Currently pursuing a degree in Marketing, Communications, or related field',
          'Knowledge of social media platforms and best practices',
          'Basic understanding of SEO and digital marketing',
          'Creative mindset with good writing skills',
          'Experience with design tools is a plus'
        ]
      },
      {
        title: 'Senior Sales Manager',
        type: 'Permanent',
        tasks: [
          'Lead and develop the sales team',
          'Establish and maintain key client relationships',
          'Develop and implement sales strategies',
          'Set and achieve sales targets',
          'Provide market analysis and forecasting',
          'Mentor junior sales staff'
        ],
        requirements: [
          '5+ years experience in real estate sales',
          'Proven track record of achieving sales targets',
          'Strong leadership and team management skills',
          'Excellent negotiation abilities',
          'Deep understanding of real estate market',
          'Valid real estate license'
        ]
      },
      {
        title: 'Property Manager',
        type: 'Permanent',
        tasks: [
          'Oversee property operations and maintenance',
          'Manage tenant relationships and lease agreements',
          'Coordinate with contractors and service providers',
          'Handle property inspections and reports',
          'Manage property budgets and financial reporting',
          'Ensure compliance with regulations'
        ],
        requirements: [
          '3+ years experience in property management',
          'Strong knowledge of property management software',
          'Excellent organizational and problem-solving skills',
          'Experience with budget management',
          'Knowledge of real estate laws and regulations',
          'Property management certification preferred'
        ]
      }
    ],
    benefits: {
      title: 'Why Join Us',
      items: [
        {
          icon: '🌟',
          title: 'Growth Opportunities',
          description: 'Continuous learning and career advancement paths'
        },
        {
          icon: '🎯',
          title: 'Competitive Package',
          description: 'Excellent compensation and benefits'
        },
        {
          icon: '🤝',
          title: 'Great Culture',
          description: 'Collaborative and inclusive work environment'
        }
      ]
    }
  },
  home: {
    hero: {
      title: 'Find Your Dream Property',
      subtitle: 'Discover exceptional properties in prime locations',
      cta: 'Start Searching',
    },
    featured: {
      title: 'Featured Properties',
      viewAll: 'View All',
    },
  },
  properties: {
    title: 'Our Properties',
    currency: 'MAD',
    noResults: 'No properties match your search criteria',
    viewDetails: 'View Details',
    notFound: 'Property Not Found',
    features: 'Features',
    interested: 'Interested in this Property?',
    schedule: 'Schedule a Visit',
    contact: 'Contact Agent',
    forRent: 'For Rent',
    forSale: 'For Sale',
    types: {
      all: 'All Properties',
      studio: 'Studios',
      apartment: 'Apartments',
      office: 'Offices'
    },
    filters: {
      title: 'Filter Properties',
      priceRange: {
        title: 'Price Range',
        min: 'Min Price',
        max: 'Max Price'
      },
      location: {
        title: 'Location',
        placeholder: 'Enter location'
      },
      propertyType: {
        title: 'Property Type',
        apartment: 'Apartment',
        house: 'House',
        villa: 'Villa',
        office: 'Office Space',
        retail: 'Retail Space',
        land: 'Land'
      },
      listingType: {
        title: 'Listing Type',
        all: 'All Properties',
        rent: 'For Rent',
        sale: 'For Sale'
      },
      apply: 'Apply Filters',
      reset: 'Reset Filters',
      active: 'Filters Active',
      searching: 'Searching...',
    },
    loading: 'Loading...',
  },
  services: {
    title: 'Our Services',
    subtitle: 'Comprehensive Real Estate Solutions',
    propertyManagement: {
      title: 'Property Management',
      description: 'Professional management of residential and commercial properties, ensuring optimal performance and tenant satisfaction.',
      features: [
        'Tenant screening and placement',
        'Rent collection and accounting',
        'Property maintenance',
        'Regular inspections'
      ]
    },
    consulting: {
      title: 'Real Estate Consulting',
      description: 'Expert advice on property investments, market analysis, and strategic planning for your real estate portfolio.',
      features: [
        'Market analysis',
        'Investment strategy',
        'Portfolio optimization',
        'Risk assessment'
      ]
    },
    rental: {
      title: 'Rental Services',
      description: 'Comprehensive rental services for both tenants and property owners, streamlining the rental process.',
      features: [
        'Property listing',
        'Tenant matching',
        'Lease preparation',
        'Move-in coordination'
      ]
    }
  },
  about: {
    title: 'About Us',
    subtitle: 'Building Dreams, Creating Value',
    mission: {
      title: 'Our Mission',
      description: 'To transform the real estate experience through innovation, integrity, and exceptional service, making every client\'s property journey a success story.',
    },
    vision: {
      title: 'Our Vision',
      description: 'To be the most trusted name in real estate, known for our commitment to excellence and our ability to create lasting value for our clients.',
    },
    values: {
      title: 'Our Values',
      items: [
        {
          title: 'Excellence',
          description: 'We strive for excellence in everything we do, setting the highest standards in the industry.'
        },
        {
          title: 'Integrity',
          description: 'We conduct our business with unwavering honesty and transparency.'
        },
        {
          title: 'Innovation',
          description: 'We embrace new technologies and ideas to provide cutting-edge solutions.'
        },
        {
          title: 'Client Focus',
          description: 'Our clients\' success is our success, and their satisfaction is our priority.'
        }
      ]
    },
    team: {
      title: 'Leadership Team',
      founder: {
        name: 'Dio De Nicola',
        position: 'Founder & CEO',
        quote: 'Real estate is about dreams. It\'s about turning dreams into reality.',
        description: 'With over 10 years of experience in real estate and property management, Dio leads with vision and innovation.'
      }
    },
    stats: {
      title: 'Our Impact',
      items: [
        {
          value: '10+',
          label: 'Years Experience'
        },
        {
          value: '6+',
          label: 'Happy Clients'
        },
        {
          value: '20+',
          label: 'Properties Managed'
        },
        {
          value: '98%',
          label: 'Client Satisfaction'
        }
      ]
    }
  },
  contact: {
    title: 'Contact Us',
    subtitle: 'Get in touch with our team',
    form: {
      name: 'Name',
      email: 'Email',
      phone: 'Phone',
      subject: 'Subject',
      message: 'Message',
      schedule: 'Preferred Meeting Time',
      contactPreference: 'Preferred Contact Method',
      fileUpload: 'Upload Documents (Optional)',
      submit: 'Send Message',
      success: 'Message sent successfully!',
      error: 'Error sending message. Please try again.',
    },
    info: {
      title: 'Contact Information',
      address: 'Casa Finance City (CFC), Casablanca Morocco',
      email: '<EMAIL>',
      phone: '+****************',
      hours: 'Monday - Friday: 9:00 AM - 6:00 PM',
    }
  },
  footer: {
    description: 'Your trusted partner in property management and real estate services.',
    quickLinks: 'Quick Links',
    followUs: 'Follow Us',
    privacy: 'Privacy Policy',
    terms: 'Terms of Service',
    links: {
      home: 'Home',
      properties: 'Properties',
      services: 'Services',
      about: 'About',
      contact: 'Contact'
    }
  },
  terms: {
    title: 'Terms of Service',
    lastUpdated: 'Last Updated: February 23, 2025',
    sections: [
      {
        title: 'Introduction',
        content: 'Welcome to Darden Property & Management. By using our services, you agree to these terms.'
      },
      {
        title: 'Use of Services',
        content: 'Our services are provided for your personal and commercial use subject to these terms.'
      },
      {
        title: 'User Responsibilities',
        content: 'Users must provide accurate information and maintain the confidentiality of their accounts.'
      }
    ]
  },
  privacy: {
    title: 'Privacy Policy',
    lastUpdated: 'Last Updated: February 23, 2025',
    sections: [
      {
        title: 'Information Collection',
        content: 'We collect information to provide better services to our users.'
      },
      {
        title: 'Data Usage',
        content: 'Your data is used to improve our services and provide personalized experiences.'
      },
      {
        title: 'Data Protection',
        content: 'We implement security measures to protect your personal information.'
      }
    ]
  },
  locations: {
    title: 'Our Premier Locations',
    learnMore: 'Learn more',
    items: [
      {
        id: 'cfc',
        name: 'Casablanca Finance City Tower',
        description: 'Designed by renowned Morphosis Architects, the CFC Tower stands as a testament to modern architecture. This 122-meter masterpiece features innovative brise-soleil elements, energy-efficient design, and aims for LEED Gold certification.',
        features: [
          'Height: 122 meters',
          'Innovative brise-soleil elements',
          'Energy-efficient design',
          'Tax incentives'
        ],
        address: 'CFC Tower, Casablanca Finance City',
        coordinates: {
          lat: 33.5731,
          lng: -7.6326
        },
        images: [
          {
            url: '/images/prop1.jpg',
            alt: 'CFC Tower Exterior'
          },
          {
            url: '/images/prop2.jpg',
            alt: 'CFC Tower Interior'
          }
        ]
      },
      {
        id: 'saray',
        name: 'Saray Project',
        description: 'A prestigious mixed-use development in the heart of Casablanca combining luxury residential spaces with premium commercial facilities. It includes expansive green spaces and modern amenities.',
        features: [
          'Area: 63,000 sqm',
          'Luxury residential and commercial spaces',
          '10,000 sqm green areas'
        ],
        address: 'Between Maârif district and Boulevard Ghandi, Casablanca',
        coordinates: {
          lat: 33.5898,
          lng: -7.6368
        },
        images: [
          {
            url: '/images/locations/saray/overview.jpg',
            alt: 'Saray Project Overview'
          },
          {
            url: '/images/locations/saray/amenities.jpg',
            alt: 'Saray Amenities'
          }
        ]
      }
    ]
  },
  cookies: {
    message: 'We use cookies to enhance your browsing experience and analyze our traffic.',
    acceptAll: 'Accept All',
    declineAll: 'Decline All',
    preferences: 'Cookie Preferences',
    savePreferences: 'Save Preferences',
    learnMore: 'Learn More',
    necessary: {
      title: 'Necessary Cookies',
      description: 'Essential cookies that enable basic website functionality and security features.'
    },
    analytics: {
      title: 'Analytics Cookies',
      description: 'Help us understand how visitors interact with our website by collecting and reporting information anonymously.'
    },
    marketing: {
      title: 'Marketing Cookies',
      description: 'Used to track visitors across websites to display relevant advertisements and measure their effectiveness.'
    },
    functional: {
      title: 'Functional Cookies',
      description: 'Enable enhanced functionality and personalization, such as language preferences and user settings.'
    }
  },
};

// French translations
const frTranslations = {
  common: {
    welcome: 'Bienvenue chez Darden Property & Management',
    search: 'Rechercher',
    menu: 'Menu',
    darkMode: 'Mode Sombre',
    language: 'Langue',
    contact: 'Contactez-nous',
  },
  navigation: {
    home: 'Accueil',
    properties: 'Propriétés',
    career: 'Carrière',
    services: 'Services',
    about: 'À Propos',
    contact: 'Contact',
    terms: 'Conditions d\'Utilisation',
    privacy: 'Politique de Confidentialité'
  },
  career: {
    title: 'Rejoignez Notre Équipe',
    subtitle: 'Construisez Votre Carrière avec Darden Property & Management',
    apply: 'Postuler Maintenant',
    responsibilities: 'Responsabilités Principales',
    requirements: 'Prérequis',
    positions: [
      {
        title: 'Stagiaire en Développement Full Stack',
        type: 'Stage',
        duration: '3-6 mois',
        tasks: [
          'Aider au développement de composants frontend React/TypeScript',
          'Travailler sur les services backend Node.js/Express',
          'Participer à la conception et à l\'implémentation de bases de données',
          'Rédiger des tests unitaires et de la documentation',
          'Collaborer avec les développeurs seniors sur l\'implémentation des fonctionnalités'
        ],
        requirements: [
          'Actuellement en cours d\'études en Informatique ou domaine connexe',
          'Connaissance de base de React et Node.js',
          'Compréhension des bases de données (SQL/NoSQL)',
          'Bonnes capacités de résolution de problèmes',
          'Fort désir d\'apprendre et de progresser'
        ]
      },
      {
        title: 'Stagiaire en Vente',
        type: 'Stage',
        duration: '3-6 mois',
        tasks: [
          'Assister aux visites de propriétés et aux réunions clients',
          'Soutenir les activités de génération et de suivi des leads',
          'Aider à maintenir la base de données CRM',
          'Préparer des rapports et présentations de vente',
          'Apprendre l\'analyse du marché immobilier'
        ],
        requirements: [
          'Actuellement en cours d\'études en Commerce, Marketing ou domaine connexe',
          'Fortes compétences en communication et relations interpersonnelles',
          'Compréhension de base des processus de vente',
          'Maîtrise de la suite MS Office',
          'Capacité à travailler dans un environnement dynamique'
        ]
      },
      {
        title: 'Stagiaire en Marketing Digital',
        type: 'Stage',
        duration: '3-6 mois',
        tasks: [
          'Assister à la gestion des réseaux sociaux et à la création de contenu',
          'Aider aux campagnes de marketing par email',
          'Soutenir les efforts d\'optimisation SEO',
          'Analyser les métriques marketing et préparer des rapports',
          'Créer et éditer le contenu des annonces immobilières'
        ],
        requirements: [
          'Actuellement en cours d\'études en Marketing, Communication ou domaine connexe',
          'Connaissance des plateformes de médias sociaux et des meilleures pratiques',
          'Compréhension de base du SEO et du marketing digital',
          'Esprit créatif avec de bonnes compétences rédactionnelles',
          'Expérience avec les outils de design est un plus'
        ]
      },
      {
        title: 'Directeur Commercial Senior',
        type: 'Permanent',
        tasks: [
          'Diriger et développer l\'équipe commerciale',
          'Établir et maintenir des relations clients clés',
          'Développer et mettre en œuvre des stratégies de vente',
          'Définir et atteindre les objectifs de vente',
          'Fournir des analyses et prévisions de marché',
          'Encadrer le personnel commercial junior'
        ],
        requirements: [
          '5+ ans d\'expérience dans la vente immobilière',
          'Historique prouvé d\'atteinte des objectifs de vente',
          'Fortes compétences en leadership et gestion d\'équipe',
          'Excellentes capacités de négociation',
          'Compréhension approfondie du marché immobilier',
          'Licence immobilière valide'
        ]
      },
      {
        title: 'Gestionnaire de Propriété',
        type: 'Permanent',
        tasks: [
          'Superviser les opérations et la maintenance des propriétés',
          'Gérer les relations avec les locataires et les contrats de location',
          'Coordonner avec les entrepreneurs et les prestataires de services',
          'Gérer les inspections et les rapports de propriété',
          'Gérer les budgets et les rapports financiers des propriétés',
          'Assurer la conformité aux réglementations'
        ],
        requirements: [
          '3+ ans d\'expérience en gestion immobilière',
          'Solide connaissance des logiciels de gestion immobilière',
          'Excellentes compétences organisationnelles et de résolution de problèmes',
          'Expérience en gestion budgétaire',
          'Connaissance des lois et réglementations immobilières',
          'Certification en gestion immobilière préférée'
        ]
      }
    ],
    benefits: {
      title: 'Pourquoi Nous Rejoindre',
      items: [
        {
          icon: '🌟',
          title: 'Opportunités de Croissance',
          description: 'Apprentissage continu et parcours d\'évolution de carrière'
        },
        {
          icon: '🎯',
          title: 'Package Compétitif',
          description: 'Excellente rémunération et avantages'
        },
        {
          icon: '🤝',
          title: 'Culture d\'Entreprise',
          description: 'Environnement de travail collaboratif et inclusif'
        }
      ]
    }
  },
  home: {
    hero: {
      title: 'Trouvez Votre Propriété de Rêve',
      subtitle: 'Découvrez des propriétés exceptionnelles dans des emplacements privilégiés',
      cta: 'Commencer la Recherche',
    },
    featured: {
      title: 'Propriétés en Vedette',
      viewAll: 'Voir Tout',
    },
  },
  properties: {
    title: 'Nos Propriétés',
    currency: 'Dh',
    noResults: 'Aucune propriété ne correspond à vos critères de recherche',
    viewDetails: 'Voir les Détails',
    notFound: 'Propriété Non Trouvée',
    features: 'Caractéristiques',
    interested: 'Intéressé par cette Propriété?',
    schedule: 'Planifier une Visite',
    contact: 'Contacter l\'Agent',
    forRent: 'À Louer',
    forSale: 'À Vendre',
    types: {
      all: 'Toutes les Propriétés',
      studio: 'Studios',
      apartment: 'Appartements',
      office: 'Bureaux'
    },
    filters: {
      title: 'Filtrer les Propriétés',
      priceRange: {
        title: 'Fourchette de Prix',
        min: 'Prix Minimum',
        max: 'Prix Maximum'
      },
      location: {
        title: 'Emplacement',
        placeholder: 'Entrer l\'emplacement'
      },
      propertyType: {
        title: 'Type de Propriété',
        apartment: 'Appartement',
        house: 'Maison',
        villa: 'Villa',
        office: 'Espace de Bureau',
        retail: 'Espace Commercial',
        land: 'Terrain'
      },
      listingType: {
        title: 'Type d\'Annonce',
        all: 'Toutes les Propriétés',
        rent: 'À Louer',
        sale: 'À Vendre'
      },
      apply: 'Appliquer les Filtres',
      reset: 'Réinitialiser les Filtres',
      active: 'Filtres Actifs',
      searching: 'Recherche en cours...',
    },
    loading: 'Chargement...',
  },
  services: {
    title: 'Nos Services',
    subtitle: 'Solutions Immobilières Complètes',
    propertyManagement: {
      title: 'Gestion Immobilière',
      description: 'Gestion professionnelle de biens résidentiels et commerciaux, assurant une performance optimale et la satisfaction des locataires.',
      features: [
        'Sélection et placement des locataires',
        'Collecte des loyers et comptabilité',
        'Property maintenance',
        'Regular inspections'
      ]
    },
    consulting: {
      title: 'Conseil Immobilier',
      description: 'Conseils d\'experts en investissement immobilier, analyse de marché et planification stratégique pour votre portefeuille immobilier.',
      features: [
        'Analyse de marché',
        'Stratégie d\'investissement',
        'Optimisation de portefeuille',
        'Évaluation des risques'
      ]
    },
    rental: {
      title: 'Services de Location',
      description: 'Services de location complets pour les locataires et les propriétaires, simplifiant le processus de location.',
      features: [
        'Annonce des biens',
        'Mise en relation locataires',
        'Préparation des baux',
        'Coordination des entrées'
      ]
    }
  },
  about: {
    title: 'À Propos de Nous',
    subtitle: 'Construire des Rêves, Créer de la Valeur',
    mission: {
      title: 'Notre Mission',
      description: 'Transformer l\'expérience immobilière grâce à l\'innovation, l\'intégrité et un service exceptionnel, faisant de chaque parcours client une histoire à succès.',
    },
    vision: {
      title: 'Notre Vision',
      description: 'Être le nom le plus fiable de l\'immobilier, reconnu pour notre engagement envers l\'excellence et notre capacité à créer une valeur durable pour nos clients.',
    },
    values: {
      title: 'Nos Valeurs',
      items: [
        {
          title: 'Excellence',
          description: 'Nous visons l\'excellence dans tout ce que nous faisons, établissant les plus hauts standards de l\'industrie.'
        },
        {
          title: 'Integrity',
          description: 'Nous menons nos activités avec une honnêteté et une transparence inébranlables.'
        },
        {
          title: 'Innovation',
          description: 'Nous adoptons les nouvelles technologies et idées pour fournir des solutions de pointe.'
        },
        {
          title: 'Focus Client',
          description: 'Le succès de nos clients est notre succès, et leur satisfaction est notre priorité.'
        }
      ]
    },
    team: {
      title: 'Équipe de Direction',
      founder: {
        name: 'Dio De Nicola',
        position: 'Fondateur & PDG',
        quote: 'L\'immobilier, c\'est une histoire de rêves. C\'est transformer les rêves en réalité.',
        description: 'Avec plus de 10 ans d\'expérience dans l\'immobilier et la gestion de propriétés, Dio dirige avec vision et innovation.'
      }
    },
    stats: {
      title: 'Notre Impact',
      items: [
        {
          value: '10+',
          label: 'Années d\'Expérience'
        },
        {
          value: '6+',
          label: 'Clients Satisfaits'
        },
        {
          value: '20+',
          label: 'Propriétés Gérées'
        },
        {
          value: '98%',
          label: 'Satisfaction Client'
        }
      ]
    }
  },
  contact: {
    title: 'Contactez-nous',
    subtitle: 'Entrez en contact avec notre équipe',
    form: {
      name: 'Nom',
      email: 'Email',
      phone: 'Téléphone',
      subject: 'Sujet',
      message: 'Message',
      schedule: 'Horaire de Rendez-vous Préféré',
      contactPreference: 'Méthode de Contact Préférée',
      fileUpload: 'Télécharger des Documents (Optionnel)',
      submit: 'Envoyer le Message',
      success: 'Message envoyé avec succès !',
      error: 'Erreur lors de l\'envoi du message. Veuillez réessayer.',
    },
    info: {
      title: 'Informations de Contact',
      address: 'City Financière Casablanca (CFC), Casablanca Maroc',
      email: '<EMAIL>',
      phone: '+****************',
      hours: 'Lundi - Vendredi: 9h00 - 18h00',
    }
  },
  footer: {
    description: 'Votre partenaire de confiance en gestion immobilière et services immobiliers.',
    quickLinks: 'Liens Rapides',
    followUs: 'Suivez-nous',
    privacy: 'Politique de Confidentialité',
    terms: 'Terms of Service',
    links: {
      home: 'Accueil',
      properties: 'Propriétés',
      services: 'Services',
      about: 'About',
      contact: 'Contact'
    }
  },
  terms: {
    title: 'Terms of Service',
    lastUpdated: 'Last Updated: February 23, 2025',
    sections: [
      {
        title: 'Introduction',
        content: 'Bienvenue chez Darden Property & Management. En utilisant nos services, vous acceptez ces conditions.'
      },
      {
        title: 'Utilisation des Services',
        content: 'Nos services sont fournis pour votre usage personnel et commercial sous réserve de ces conditions.'
      },
      {
        title: 'Responsabilités de l\'Utilisateur',
        content: 'Les utilisateurs doivent fournir des informations exactes et maintenir la confidentialité de leurs comptes.'
      }
    ]
  },
  privacy: {
    title: 'Privacy Policy',
    lastUpdated: 'Last Updated: February 23, 2025',
    sections: [
      {
        title: 'Information Collection',
        content: 'We collect information to provide better services to our users.'
      },
      {
        title: 'Data Usage',
        content: 'Your data is used to improve our services and provide personalized experiences.'
      },
      {
        title: 'Data Protection',
        content: 'We implement security measures to protect your personal information.'
      }
    ]
  },
  locations: {
    title: 'Our Premier Locations',
    learnMore: 'Learn more',
    items: [
      {
        id: 'cfc',
        name: 'Tour Casablanca Finance City',
        description: 'Conçue par les célèbres architectes Morphosis, la Tour CFC est un témoignage de l\'architecture moderne. Ce chef-d\'œuvre de 122 mètres présente des éléments brise-soleil innovants, une conception écoénergétique et vise la certification LEED Gold.',
        features: [
          'Hauteur : 122 mètres',
          'Éléments brise-soleil innovants',
          'Conception écoénergétique',
          'Avantages fiscaux'
        ],
        address: 'Tour CFC, Casablanca Finance City',
        coordinates: {
          lat: 33.5731,
          lng: -7.6326
        },
        images: [
          {
            url: '/images/locations/cfc/exterior.jpg',
            alt: 'Extérieur de la Tour CFC'
          },
          {
            url: '/images/locations/cfc/interior.jpg',
            alt: 'Intérieur de la Tour CFC'
          }
        ]
      },
      {
        id: 'saray',
        name: 'Projet Saray',
        description: 'Un prestigieux développement à usage mixte au cœur de Casablanca combinant des espaces résidentiels de luxe avec des installations commerciales haut de gamme. Il comprend de vastes espaces verts et des équipements modernes.',
        features: [
          'Superficie : 63 000 m²',
          'Espaces résidentiels et commerciaux de luxe',
          '10 000 m² d\'espaces verts'
        ],
        address: 'Entre le quartier Maârif et le Boulevard Ghandi, Casablanca',
        coordinates: {
          lat: 33.5898,
          lng: -7.6368
        },
        images: [
          {
            url: '/images/locations/saray/overview.jpg',
            alt: 'Vue d\'ensemble du Projet Saray'
          },
          {
            url: '/images/locations/saray/amenities.jpg',
            alt: 'Équipements Saray'
          }
        ]
      }
    ]
  },
  cookies: {
    message: 'Nous utilisons des cookies pour améliorer votre expérience de navigation et analyser notre trafic.',
    acceptAll: 'Tout accepter',
    declineAll: 'Tout refuser',
    preferences: 'Préférences des cookies',
    savePreferences: 'Enregistrer les préférences',
    learnMore: 'En savoir plus',
    necessary: {
      title: 'Cookies Nécessaires',
      description: 'Cookies essentiels permettant les fonctionnalités de base du site et ses fonctions de sécurité.'
    },
    analytics: {
      title: 'Cookies Analytiques',
      description: 'Nous aident à comprendre comment les visiteurs interagissent avec notre site en collectant et rapportant des informations de manière anonyme.'
    },
    marketing: {
      title: 'Cookies Marketing',
      description: 'Utilisés pour suivre les visiteurs sur les sites web afin d\'afficher des publicités pertinentes et mesurer leur efficacité.'
    },
    functional: {
      title: 'Cookies Fonctionnels',
      description: 'Permettent des fonctionnalités et une personnalisation améliorées, comme les préférences de langue et les paramètres utilisateur.'
    }
  },
};

i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: { translation: enTranslations },
      fr: { translation: frTranslations },
    },
    lng: 'en',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;
