import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const HeroSection: React.FC = () => {
  const { t } = useTranslation();

  return (
    <section className="relative min-h-[80vh] flex items-center justify-center overflow-hidden">
      {/* Dynamic background with patterns */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute inset-0"
          style={{
            background: `
              radial-gradient(circle at 0% 0%, rgba(0,140,69,0.15) 0%, transparent 50%),
              linear-gradient(45deg, rgba(0,140,69,0.05) 25%, transparent 25%, transparent 75%, rgba(0,140,69,0.05) 75%),
              linear-gradient(-45deg, rgba(0,140,69,0.05) 25%, transparent 25%, transparent 75%, rgba(0,140,69,0.05) 75%)
            `,
            backgroundSize: '100% 100%, 60px 60px, 60px 60px'
          }}
          animate={{
            backgroundPosition: [
              '0% 0%, 0px 0px, 0px 0px',
              '100% 100%, 60px 60px, -60px -60px'
            ]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        
        {/* Interactive decorative shapes */}
        <div className="absolute inset-0 pointer-events-none mix-blend-plus-lighter">
          {[...Array(5)].map((_, i) => (
            <motion.div
              key={`shape-${i}`}
              className="absolute rounded-full mix-blend-screen animate-glow"
              style={{
                width: `${80 + i * 40}px`,
                height: `${80 + i * 40}px`,
                background: `
                  radial-gradient(circle at center, 
                    rgba(0,140,69,${0.2 - i * 0.03}) 0%, 
                    rgba(0,179,89,${0.15 - i * 0.02}) 35%,
                    transparent 70%
                  )
                `,
                filter: 'blur(8px) drop-shadow(0 0 8px rgba(0,140,69,0.3))',
                top: `${15 + Math.sin(i * 1.5) * 20}%`,
                left: `${10 + Math.cos(i * 1.2) * 15}%`,
              }}
              animate={{
                scale: [1, 1.05, 1],
                opacity: [0.7, 0.8, 0.7],
                y: [0, Math.sin(i * 0.8) * 15, 0],
                x: [0, Math.cos(i * 0.8) * 15, 0],
              }}
              transition={{
                duration: 8 + i,
                repeat: Infinity,
                ease: "easeInOut",
                times: [0, 0.5, 1]
              }}
            />
          ))}
        </div>

        {/* Animated lines */}
        <svg className="absolute inset-0 w-full h-full opacity-20" style={{ filter: 'blur(1px)' }}>
          <motion.path
            d="M0,50 Q250,0 500,50 T1000,50"
            stroke="rgba(0,140,69,0.3)"
            strokeWidth="1"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "linear"
            }}
          />
          <motion.path
            d="M0,150 Q250,100 500,150 T1000,150"
            stroke="rgba(0,140,69,0.2)"
            strokeWidth="1"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "linear",
              delay: 0.5
            }}
          />
        </svg>
      </div>

      {/* Property Showcase Carousel */}
      <div className="container mx-auto px-4 relative z-10">
        <div className="carousel max-w-6xl mx-auto">
          {[
            {
              id: 'cfc-tower',
              image: '/images/prop1.jpg',
              title: 'CFC Tower',
              location: 'Casablanca Finance City',
              price: `${(2500000).toLocaleString()} ${t('properties.currency')}`,
            },
            {
              id: 'saray-project',
              image: '/images/prop2.jpg',
              title: 'Saray Project',
              location: 'Premium Location',
              price: `${(3200000).toLocaleString()} ${t('properties.currency')}`,
            }
          ].map((property, index) => (
            <motion.div
              key={property.id}
              className="carousel-slide relative"
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: index * 0.2 }}
            >
              <img
                src={property.image}
                alt={property.title}
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
              <div className="absolute inset-0 bg-black bg-opacity-30 flex flex-col justify-end p-6">
                <h3 className="text-2xl font-bold text-white">{property.title}</h3>
                <p className="text-lg text-white mb-2">{property.location}</p>
                <p className="text-lg text-white">{property.price}</p>
                <div className="mt-4 flex space-x-4">
                  <Link
                    to={`/properties/${property.id}`}
                    className="bg-italian-green text-white px-4 py-2 rounded-md hover:bg-italian-green-light transition"
                  >
                    View Details
                  </Link>
                  <button className="bg-white text-italian-green px-4 py-2 rounded-md hover:bg-gray-100 transition">
                    Save Property
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Enhanced bottom wave */}
      <div className="absolute bottom-0 left-0 right-0 overflow-hidden">
        <motion.div
          className="h-24 bg-gradient-to-t from-white to-transparent dark:from-gray-900 dark:to-transparent"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.8 }}
        />
        <motion.div
          className="absolute bottom-0 left-0 right-0 h-32"
          style={{
            background: 'linear-gradient(45deg, rgba(0,140,69,0.1) 0%, transparent 75%)',
            maskImage: 'linear-gradient(to bottom, transparent, black)'
          }}
          animate={{
            x: ['-100%', '100%']
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

    </section>
  );
};

export default HeroSection;
